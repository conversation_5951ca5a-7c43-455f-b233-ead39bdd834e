package com.ffcs.oss.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ffcs.oss.domain.AiAssistantD;
import com.ffcs.oss.domain.AiPermissionConfigD;
import com.ffcs.oss.param.out.ServiceResp;
import com.ffcs.oss.param.vm.QueryPageVm;
import com.ffcs.oss.web.rest.evt.permission.PermissionConfigEvt;
import com.ffcs.oss.web.rest.vm.permission.PermissionConfigVm;
import java.util.List;
/**
 * 权限配置服务接口
 */
public interface AiPermissionConfigService extends IService<AiPermissionConfigD> {
    
    /**
     * 获取权限配置列表
     *
     * @param evt 查询参数
     * @return 权限配置列表
     */
    QueryPageVm<PermissionConfigVm> getPermissionConfigList(PermissionConfigEvt evt);
    
    /**
     * 批量添加权限配置
     *
     * @param evt 权限配置参数
     * @return 操作结果
     */
    ServiceResp batchAddPermissionConfig(PermissionConfigEvt evt);
    
    /**
     * 删除权限配置
     *
     * @param evt 权限配置参数
     * @return 操作结果
     */
    ServiceResp deletePermissionConfig(PermissionConfigEvt evt);
    
    /**
     * 检查用户是否有资源的管理权限
     *
     * @param resourceType 资源类型
     * @param resourceId 资源ID
     * @param userName 用户名
     * @return 是否有管理权限
     */
    boolean hasAdminPermission(Integer resourceType, String resourceId, String userName);
    
    /**
     * 检查用户是否有资源的使用权限
     *
     * @param resourceType 资源类型
     * @param resourceId 资源ID
     * @param userName 用户名
     * @return 是否有使用权限
     */
    boolean hasUsePermission(Integer resourceType, String resourceId, String userName);
    
    /**
     * 为资源创建者添加管理权限
     *
     * @param resourceType 资源类型
     * @param resourceId 资源ID
     * @param creatorName 创建者用户名
     * @return 操作结果
     */
    ServiceResp addCreatorAdminPermission(Integer resourceType, String resourceId, String creatorName);

    /**
     * 批量添加权限配置并自动分配关联资源权限
     * 当分配助理权限时，自动分配关联的数据模型和数据源权限
     *
     * @param evt 权限配置参数
     * @return 操作结果
     */
    ServiceResp batchAddPermissionConfigWithCascade(PermissionConfigEvt evt);

    /**
     * 删除权限配置并处理关联资源权限
     * 当删除助理权限时，检查并清理不再需要的数据模型和数据源权限
     *
     * @param evt 权限配置参数
     * @return 操作结果
     */
    ServiceResp deletePermissionConfigWithCascade(PermissionConfigEvt evt);

    /**
     * 为助理权限自动分配关联的数据模型和数据源权限
     *
     * @param assistantId 助理ID
     * @param permissionType 权限类型
     * @param userType 用户/用户组类型
     * @param userIdList 用户/用户组ID列表
     * @param userNameList 用户/用户组名称列表
     * @param userAliasList 用户/用户组别名列表
     * @param currentUserName 当前操作用户
     * @return 操作结果
     */
    ServiceResp autoAssignRelatedPermissions(String assistantId, Integer permissionType,
                                           Integer userType, List<String> userIdList,
                                           List<String> userNameList, List<String> userAliasList, String currentUserName);

    /**
     * 处理助理修改时的权限重新分配
     * 当助理的数据模型或数据源发生变化时，重新分配相关权限
     *
     * @param assistantId 助理ID
     * @param oldAssistant 修改前的助理信息
     * @param newAssistant 修改后的助理信息
     * @param currentUserName 当前操作用户
     * @return 操作结果
     */
    ServiceResp handleAssistantUpdatePermissions(String assistantId, AiAssistantD oldAssistant,
                                               AiAssistantD newAssistant, String currentUserName);

    /**
     * 检查并清理不再需要的关联资源权限
     *
     * @param resourceType 被删除权限的资源类型
     * @param resourceId 被删除权限的资源ID
     * @param userType 用户/用户组类型
     * @param userId 用户/用户组ID
     * @param userName 用户/用户组名称
     * @param currentUserName 当前操作用户
     * @return 操作结果
     */
    ServiceResp cleanupUnusedRelatedPermissions(Integer resourceType, String resourceId,
                                              Integer userType, String userId, String userName,
                                              String currentUserName);
} 