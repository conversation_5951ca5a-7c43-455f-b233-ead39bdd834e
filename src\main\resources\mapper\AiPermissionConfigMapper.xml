<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ffcs.oss.mapper.AiPermissionConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ffcs.oss.domain.AiPermissionConfigD">
        <id column="permission_id" property="permissionId" />
        <result column="resource_type" property="resourceType" />
        <result column="resource_id" property="resourceId" />
        <result column="permission_type" property="permissionType" />
        <result column="user_type" property="userType" />
        <result column="user_id" property="userId" />
        <result column="user_name" property="userName" />
        <result column="user_alias" property="userAlias" />
        <result column="create_user_name" property="createUserName" />
        <result column="create_time" property="createTime" />
        <result column="update_user_name" property="updateUserName" />
        <result column="update_time" property="updateTime" />
    </resultMap>
    
    <!-- 权限配置视图映射结果 -->
    <resultMap id="PermissionConfigVmMap" type="com.ffcs.oss.web.rest.vm.permission.PermissionConfigVm">
        <id column="permission_id" property="permissionId" />
        <result column="resource_type" property="resourceType" />
        <result column="resource_id" property="resourceId" />
        <result column="resource_name" property="resourceName" />
        <result column="permission_type" property="permissionType" />
        <result column="user_type" property="userType" />
        <result column="user_id" property="userId" />
        <result column="user_name" property="userName" />
        <result column="user_alias" property="userAlias" />
        <result column="create_user_name" property="createUserName" />
        <result column="create_time" property="createTime" />
        <result column="update_user_name" property="updateUserName" />
        <result column="update_time" property="updateTime" />
        <result column="is_creator" property="isCreator" />
    </resultMap>

    <!-- 查询权限配置列表 -->
    <select id="getPermissionConfigList" parameterType="com.ffcs.oss.web.rest.evt.permission.PermissionConfigEvt" resultMap="PermissionConfigVmMap">
        SELECT
            pc.*,
            CASE
                WHEN pc.resource_type = 1 THEN dm.model_name
                WHEN pc.resource_type = 2 THEN a.assistant_name
                WHEN pc.resource_type = 3 THEN ds.connection_name
                ELSE ''
            END AS resource_name,
            CASE
                WHEN pc.resource_type = 1 AND pc.user_name = dm.create_user_name THEN true
                WHEN pc.resource_type = 2 AND pc.user_name = a.create_user_name THEN true
                WHEN pc.resource_type = 3 AND pc.user_name = ds.creator_name THEN true
                ELSE false
            END AS is_creator
        FROM
            ai_permission_config_d pc
        LEFT JOIN
            ai_data_model_d dm ON pc.resource_type = 1 AND pc.resource_id = dm.data_model_id
        LEFT JOIN
            ai_assistant_d a ON pc.resource_type = 2 AND pc.resource_id = a.assistant_id::varchar
        LEFT JOIN
            ai_db_connection_config ds ON pc.resource_type = 3 AND pc.resource_id = ds.connection_id::varchar
        <where>
            <if test="resourceType != null">
                AND pc.resource_type = #{resourceType}
            </if>
            <if test="resourceId != null and resourceId != ''">
                AND pc.resource_id = #{resourceId}
            </if>
            <if test="permissionType != null">
                AND pc.permission_type = #{permissionType}
            </if>
            <if test="userType != null">
                AND pc.user_type = #{userType}
            </if>
            <if test="userId != null and userId != ''">
                AND pc.user_id = #{userId}
            </if>
            <if test="userName != null and userName != ''">
                AND pc.user_name like concat('%',#{userName},'%')
            </if>
            <if test="keyword != null and keyword != ''">
                AND (
                    pc.user_name LIKE CONCAT('%', #{keyword}, '%')
                    OR pc.user_alias LIKE CONCAT('%', #{keyword}, '%')
                    OR
                    CASE
                        WHEN pc.resource_type = 1 THEN dm.model_name
                        WHEN pc.resource_type = 2 THEN a.assistant_name
                        WHEN pc.resource_type = 3 THEN ds.connection_name
                        ELSE ''
                    END LIKE CONCAT('%', #{keyword}, '%')
                )
            </if>
        </where>
        ORDER BY pc.create_time DESC
    </select>
    
    <!-- 查询资源的创建者 -->
    <select id="getResourceCreator" resultType="java.lang.String">
        SELECT
            CASE
                WHEN #{resourceType} = 1 THEN (SELECT create_user_name FROM ai_data_model_d WHERE data_model_id = #{resourceId}::varchar)
                WHEN #{resourceType} = 2 THEN (SELECT create_user_name FROM ai_assistant_d WHERE assistant_id::varchar = #{resourceId})
                WHEN #{resourceType} = 3 THEN (SELECT creator_name FROM ai_db_connection_config WHERE connection_id::varchar = #{resourceId})
                ELSE ''
            END
    </select>
    
    <!-- 检查用户是否有资源的管理权限 -->
    <select id="checkUserAdminPermission" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM ai_permission_config_d
        WHERE resource_type = #{resourceType}
        AND resource_id = #{resourceId}
        <choose>
            <when test="groupIdList != null and groupIdList.size() > 0">
                AND user_id::numeric in
                <foreach collection="groupIdList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                AND user_type = 2
            </when>
            <otherwise>
                AND user_type = 1
                AND user_name = #{userName}
            </otherwise>
        </choose>
        AND permission_type = 1
    </select>
    
    <!-- 检查用户是否有资源的使用权限 -->
    <select id="checkUserUsePermission" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM ai_permission_config_d
        WHERE resource_type = #{resourceType}
        AND resource_id = #{resourceId}
        AND (permission_type = 1 OR permission_type = 2)
        <choose>
            <when test="groupIdList != null and groupIdList.size() > 0">
                AND user_id::numeric in
                <foreach collection="groupIdList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                AND user_type = 2
            </when>
            <otherwise>
                AND user_type = 1
                AND user_name = #{userName}
            </otherwise>
        </choose>
    </select>
</mapper> 