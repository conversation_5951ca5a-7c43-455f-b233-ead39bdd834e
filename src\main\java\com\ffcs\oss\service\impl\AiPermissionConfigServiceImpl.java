package com.ffcs.oss.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ffcs.oss.client.SmQryClient;
import com.ffcs.oss.common.security.utils.PtSecurityUtils;
import com.ffcs.oss.common.utils.collection.CollectionUtil;
import com.ffcs.oss.config.i18n.LocalMessageTool;
import com.ffcs.oss.domain.AiAssistantD;
import com.ffcs.oss.domain.AiDataModelD;
import com.ffcs.oss.domain.AiPermissionConfigD;
import com.ffcs.oss.mapper.AiPermissionConfigMapper;
import com.ffcs.oss.param.out.ServiceResp;
import com.ffcs.oss.param.vm.QueryPageVm;
import com.ffcs.oss.service.AiAssistantService;
import com.ffcs.oss.service.AiDataModelService;
import com.ffcs.oss.service.AiPermissionConfigService;
import com.ffcs.oss.service.DbConnectionService;
import com.ffcs.oss.service.UserService;
import com.ffcs.oss.utils.StringUtils;
import com.ffcs.oss.web.rest.constant.CommonConstant;
import com.ffcs.oss.web.rest.constant.DbConnectionConstant;
import com.ffcs.oss.web.rest.constant.LocaleKeyConstant;
import com.ffcs.oss.web.rest.enums.UserTypeEnum;
import com.ffcs.oss.web.rest.evt.permission.PermissionConfigEvt;
import com.ffcs.oss.web.rest.evt.user.QryUserEvt;
import com.ffcs.oss.web.rest.evt.user.UserIdsEvt;
import com.ffcs.oss.web.rest.vm.permission.PermissionConfigVm;
import com.ffcs.oss.web.rest.vm.user.SimpleUserVm;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;

import java.math.BigInteger;
import java.util.Arrays;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 权限配置服务实现类
 */
@Slf4j
@Service
public class AiPermissionConfigServiceImpl extends ServiceImpl<AiPermissionConfigMapper, AiPermissionConfigD> implements AiPermissionConfigService {

    @Autowired
    private AiPermissionConfigMapper permissionConfigMapper;

    @Autowired
    private UserService userService;
    @Autowired
    private SmQryClient smQryClient;
    @Autowired
    private AiAssistantService aiAssistantService;
    @Autowired
    private AiDataModelService aiDataModelService;

    @Autowired
    private DbConnectionService dbConnectionService;

    @Override
    public QueryPageVm<PermissionConfigVm> getPermissionConfigList(PermissionConfigEvt evt) {
        if (evt.getPageNo() != null && evt.getPageSize() != null && evt.isCountTotal()) {
            Page page = PageHelper.startPage(evt.getPageNo(), evt.getPageSize(), evt.isCountTotal());
            List<PermissionConfigVm> permissionList = permissionConfigMapper.getPermissionConfigList(evt);
            return QueryPageVm.getInstance(evt, permissionList, page.getTotal());
        } else {
            List<PermissionConfigVm> permissionList = permissionConfigMapper.getPermissionConfigList(evt);
            return QueryPageVm.getInstance(evt, permissionList, 0L);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ServiceResp batchAddPermissionConfig(PermissionConfigEvt evt) {
        // 参数校验
        if (evt.getResourceType() == null) {
            return ServiceResp.getInstance().error("资源类型不能为空");
        }
        if (StringUtils.isBlank(evt.getResourceId())) {
            return ServiceResp.getInstance().error("资源ID不能为空");
        }
        if (evt.getPermissionType() == null) {
            return ServiceResp.getInstance().error("权限类型不能为空");
        }
        if (evt.getUserType() == null) {
            return ServiceResp.getInstance().error("用户/用户组类型不能为空");
        }
        if (evt.getUserIdList() == null || evt.getUserIdList().isEmpty()) {
            return ServiceResp.getInstance().error("用户/用户组ID列表不能为空");
        }
        if (evt.getUserNameList() == null || evt.getUserNameList().isEmpty()) {
            return ServiceResp.getInstance().error("用户/用户组名称列表不能为空");
        }
        if (evt.getUserIdList().size() != evt.getUserNameList().size()) {
            return ServiceResp.getInstance().error("用户/用户组ID列表与名称列表长度不一致");
        }
        // 检查userAliasList参数（可选）
        if (evt.getUserAliasList() != null && !evt.getUserAliasList().isEmpty()) {
            if (evt.getUserIdList().size() != evt.getUserAliasList().size()) {
                return ServiceResp.getInstance().error("用户/用户组ID列表与别名列表长度不一致");
            }
        }

        // 获取当前用户
        String currentUserName = userService.getCurrentUserName();

        // 检查当前用户是否有该资源的管理权限
        if (!hasAdminPermission(evt.getResourceType(), evt.getResourceId(), currentUserName)) {
            return ServiceResp.getInstance().error("您没有该资源的管理权限");
        }

        // 获取资源创建者
        String creator = permissionConfigMapper.getResourceCreator(evt.getResourceType(), evt.getResourceId());

        // 批量添加权限配置
        List<AiPermissionConfigD> permissionList = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();

        for (int i = 0; i < evt.getUserIdList().size(); i++) {
            String userId = evt.getUserIdList().get(i);
            String userName = evt.getUserNameList().get(i);
            String userAlias = null;
            if (evt.getUserAliasList() != null && i < evt.getUserAliasList().size()) {
                userAlias = evt.getUserAliasList().get(i);
            }

            // 如果是创建者，不允许修改权限类型
            if (userName.equals(creator)) {
                continue;
            }

            // 检查是否已存在相同的权限配置
            LambdaQueryWrapper<AiPermissionConfigD> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AiPermissionConfigD::getResourceType, evt.getResourceType())
                    .eq(AiPermissionConfigD::getResourceId, evt.getResourceId())
                    .eq(AiPermissionConfigD::getUserType, evt.getUserType())
                    .eq(AiPermissionConfigD::getUserId, userId);

            if (this.count(queryWrapper) > 0) {
                // 已存在，更新权限类型
                AiPermissionConfigD existingPermission = this.getOne(queryWrapper);
                existingPermission.setPermissionType(evt.getPermissionType());
                existingPermission.setUserAlias(userAlias);
                existingPermission.setUpdateUserName(currentUserName);
                existingPermission.setUpdateTime(now);
                permissionList.add(existingPermission);
            } else {
                // 不存在，添加新的权限配置
                AiPermissionConfigD permission = new AiPermissionConfigD();
                permission.setResourceType(evt.getResourceType());
                permission.setResourceId(evt.getResourceId());
                permission.setPermissionType(evt.getPermissionType());
                permission.setUserType(evt.getUserType());
                permission.setUserId(userId);
                permission.setUserName(userName);
                permission.setUserAlias(userAlias);
                permission.setCreateUserName(currentUserName);
                permission.setCreateTime(now);
                permission.setUpdateUserName(currentUserName);
                permission.setUpdateTime(now);
                permissionList.add(permission);
            }
        }

        // 批量保存或更新
        if (CollectionUtil.isNotEmpty(permissionList)){
            this.saveOrUpdateBatch(permissionList);
        }

        return ServiceResp.getInstance().success(LocalMessageTool.getMessage(LocaleKeyConstant.OPERATE_SUCCESS));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ServiceResp deletePermissionConfig(PermissionConfigEvt evt) {
        // 参数校验
        if (evt.getPermissionId() == null) {
            return ServiceResp.getInstance().error("权限ID不能为空");
        }

        // 获取权限配置
        AiPermissionConfigD permission = this.getById(evt.getPermissionId());
        if (permission == null) {
            return ServiceResp.getInstance().error("权限配置不存在");
        }

        // 获取当前用户
        String currentUserName = userService.getCurrentUserName();

        // 检查当前用户是否有该资源的管理权限
        if (!hasAdminPermission(permission.getResourceType(), permission.getResourceId(), currentUserName)) {
            return ServiceResp.getInstance().error("您没有该资源的管理权限");
        }

        // 获取资源创建者
        String creator = permissionConfigMapper.getResourceCreator(permission.getResourceType(), permission.getResourceId());

        // 如果是创建者，不允许删除
        if (permission.getUserName().equals(creator)) {
            return ServiceResp.getInstance().error("不能删除创建者的权限");
        }

        // 删除权限配置
        boolean result = this.removeById(evt.getPermissionId());

        return result ?
                ServiceResp.getInstance().success(LocalMessageTool.getMessage(LocaleKeyConstant.OPERATE_SUCCESS)) :
                ServiceResp.getInstance().error(LocalMessageTool.getMessage(LocaleKeyConstant.OPERATE_ZERO_RECORDS));
    }

    @Override
    public boolean hasAdminPermission(Integer resourceType, String resourceId, String userName) {
        if (resourceType == null || StringUtils.isBlank(resourceId) || StringUtils.isBlank(userName)) {
            return false;
        }
        QryUserEvt user = new QryUserEvt();
        user.setLoginName(userService.getCurrentUserName());
        SimpleUserVm userVm = smQryClient.qrySimpleUser(user).getBody();
        if (userVm != null && userVm.getUserType().equals(CommonConstant.YES)) {
            return true;
        }

        // 检查是否是资源创建者
        String creator = permissionConfigMapper.getResourceCreator(resourceType, resourceId);
        if (userName.equals(creator)) {
            return true;
        }

        int groupCount;
        try{
            ServiceResp groupIdByUserName = smQryClient.getGroupIdByUserName(userService.getCurrentUserName());
            if (groupIdByUserName.isSuccess()) {
                List<Long> groupIdList = (List<Long>) groupIdByUserName.getBody();
                if (CollectionUtil.isNotEmpty(groupIdList)) {
                    groupCount = permissionConfigMapper.checkUserUsePermission(resourceType, resourceId, userName,groupIdList);
                    if (groupCount>0){
                        return true;
                    }
                }
            }
        }catch (Exception e){
            log.error("通过loginName{},获取用户组信息错误",userName,e);
        }

        // 检查是否有管理权限
        int count = permissionConfigMapper.checkUserAdminPermission(resourceType, resourceId, userName,null);
        return count > 0;
    }

    @Override
    public boolean hasUsePermission(Integer resourceType, String resourceId, String userName) {
        if (resourceType == null || StringUtils.isBlank(resourceId) || StringUtils.isBlank(userName)) {
            return false;
        }

        QryUserEvt user = new QryUserEvt();
        user.setLoginName(PtSecurityUtils.getUsername());
        SimpleUserVm userVm = smQryClient.qrySimpleUser(user).getBody();
        if (userVm != null && userVm.getUserType().equals(CommonConstant.YES)) {
            return true;
        }

        // 检查是否是资源创建者
        String creator = permissionConfigMapper.getResourceCreator(resourceType, resourceId);
        if (userName.equals(creator)) {
            return true;
        }

        int groupCount;
        try{
            ServiceResp groupIdByUserName = smQryClient.getGroupIdByUserName(userService.getCurrentUserName());
            if (groupIdByUserName.isSuccess()) {
                List<Long> groupIdList = (List<Long>) groupIdByUserName.getBody();
                if (CollectionUtil.isNotEmpty(groupIdList)) {
                    groupCount = permissionConfigMapper.checkUserUsePermission(resourceType, resourceId, userName,groupIdList);
                    if (groupCount>0){
                        return true;
                    }
                }
            }
        }catch (Exception e){
            log.error("通过loginName{},获取用户组信息错误",userName,e);
        }

        // 检查是否有使用权限
        int count = permissionConfigMapper.checkUserUsePermission(resourceType, resourceId, userName,null);
        return count > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ServiceResp addCreatorAdminPermission(Integer resourceType, String resourceId, String creatorName) {
        if (resourceType == null || StringUtils.isBlank(resourceId) || StringUtils.isBlank(creatorName)) {
            return ServiceResp.getInstance().error("参数不完整");
        }

        // 检查是否已存在创建者权限配置
        LambdaQueryWrapper<AiPermissionConfigD> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiPermissionConfigD::getResourceType, resourceType)
                .eq(AiPermissionConfigD::getResourceId, resourceId)
                .eq(AiPermissionConfigD::getUserName, creatorName);

        if (this.count(queryWrapper) > 0) {
            // 已存在，无需重复添加
            return ServiceResp.getInstance().success(LocalMessageTool.getMessage(LocaleKeyConstant.OPERATE_SUCCESS));
        }

        // 添加创建者管理权限
        AiPermissionConfigD permission = new AiPermissionConfigD();
        permission.setResourceType(resourceType);
        permission.setResourceId(resourceId);
        permission.setPermissionType(1); // 管理员权限
        permission.setUserType(1); // 用户类型
        permission.setUserId(creatorName); // 使用用户名作为用户ID
        permission.setUserName(creatorName);
        permission.setUserAlias(null); // 创建者权限时没有别名信息
        permission.setCreateUserName(creatorName);
        permission.setCreateTime(LocalDateTime.now());
        permission.setUpdateUserName(creatorName);
        permission.setUpdateTime(LocalDateTime.now());

        boolean result = this.save(permission);

        return result ?
                ServiceResp.getInstance().success(LocalMessageTool.getMessage(LocaleKeyConstant.OPERATE_SUCCESS)) :
                ServiceResp.getInstance().error(LocalMessageTool.getMessage(LocaleKeyConstant.OPERATE_ZERO_RECORDS));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ServiceResp batchAddPermissionConfigWithCascade(PermissionConfigEvt evt) {
        if (CollectionUtil.isNotEmpty(evt.getUserIdList())&& UserTypeEnum.USER.getCode().equals(evt.getUserType())){
            UserIdsEvt userIdsEvt = new UserIdsEvt();
            List<Long> userIdList = evt.getUserIdList().stream().map(Long::parseLong).collect(Collectors.toList());
            userIdsEvt.setUserIds(userIdList);
            ServiceResp<List<com.ffcs.oss.web.rest.vm.newUser.UserVm>> userByIdsResp = smQryClient.getUserByIds(userIdsEvt);
            if (userByIdsResp.isSuccess()){
                List<com.ffcs.oss.web.rest.vm.newUser.UserVm> userByIds =userByIdsResp.getBody();
                if (CollectionUtil.isNotEmpty(userByIds)){
                    evt.setUserNameList(userByIds.stream().map(com.ffcs.oss.web.rest.vm.newUser.UserVm::getLoginName).collect(Collectors.toList()));
                    // 同时设置用户别名列表
                    evt.setUserAliasList(userByIds.stream().map(com.ffcs.oss.web.rest.vm.newUser.UserVm::getAlias).collect(Collectors.toList()));
                }
            }
        }
        // 首先执行原有的权限分配逻辑
        ServiceResp result = batchAddPermissionConfig(evt);

        // 如果是助理权限分配且成功，则自动分配关联资源权限
        if (result.isSuccess() && evt.getResourceType().equals(CommonConstant.RESOURCE_TYPE_ASSISTANT)) {
            String currentUserName = userService.getCurrentUserName();
            ServiceResp cascadeResult = autoAssignRelatedPermissions(
                evt.getResourceId(),
                evt.getPermissionType(),
                evt.getUserType(),
                evt.getUserIdList(),
                evt.getUserNameList(),
                evt.getUserAliasList(),
                currentUserName
            );

            // 如果级联分配失败，记录日志但不影响主要操作
            if (!cascadeResult.isSuccess()) {
                log.warn("助理:{},权限分配成功，但关联资源权限自动分配失败", evt.getResourceId());
            }
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ServiceResp deletePermissionConfigWithCascade(PermissionConfigEvt evt) {
        // 获取要删除的权限配置信息
        AiPermissionConfigD permission = this.getById(evt.getPermissionId());
        if (permission == null) {
            return ServiceResp.getInstance().error("权限配置不存在");
        }

        // 如果是数据模型权限，检查是否被助理引用
        if (permission.getResourceType().equals(CommonConstant.RESOURCE_TYPE_DATA_MODEL)) {
            boolean isReferenced = aiDataModelService.checkModelReferenceByAssistant(permission.getResourceId());
            if (isReferenced) {
                return ServiceResp.getInstance().error("该数据模型正在被助理引用，无法取消权限");
            }
        }

        // 如果是数据源权限，检查是否被助理引用
        if (permission.getResourceType().equals(DbConnectionConstant.RESOURCE_TYPE_DB_CONNECTION)) {
            boolean isReferenced = dbConnectionService.checkConnectionReferenceByAssistant(permission.getResourceId());
            if (isReferenced) {
                return ServiceResp.getInstance().error("该数据源正在被助理引用，无法取消权限");
            }
        }

        // 执行原有的权限删除逻辑
        ServiceResp result = deletePermissionConfig(evt);

        // 如果是助理权限删除且成功，则检查并清理关联资源权限
        if (result.isSuccess() && permission.getResourceType().equals(CommonConstant.RESOURCE_TYPE_ASSISTANT)) {
            String currentUserName = userService.getCurrentUserName();
            ServiceResp cleanupResult = cleanupUnusedRelatedPermissions(
                permission.getResourceType(),
                permission.getResourceId(),
                permission.getUserType(),
                permission.getUserId(),
                permission.getUserName(),
                currentUserName
            );

            // 如果清理失败，记录日志但不影响主要操作
            if (!cleanupResult.isSuccess()) {
                log.warn("助理:{},权限删除成功，但关联资源权限清理失败", evt.getResourceId());
            }
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ServiceResp autoAssignRelatedPermissions(String assistantId, Integer permissionType,
                                                   Integer userType, List<String> userIdList,
                                                   List<String> userNameList, List<String> userAliasList, String currentUserName) {
        try {
            // 获取助理信息
            AiAssistantD assistant = aiAssistantService.getById(Long.valueOf(assistantId));
            if (assistant == null) {
                log.error("助理不存在，无法自动分配关联权限: assistantId={}", assistantId);
                return ServiceResp.getInstance().success("助理不存在，跳过关联权限分配");
            }

            List<String> successMessages = new ArrayList<>();
            List<String> errorMessages = new ArrayList<>();

            // 1. 分配数据模型权限
            if (StringUtils.isNotBlank(assistant.getDataModelId())) {
                ServiceResp dataModelResult = assignPermissionsToResource(
                    CommonConstant.RESOURCE_TYPE_DATA_MODEL,
                    assistant.getDataModelId(),
                    permissionType,
                    userType,
                    userIdList,
                    userNameList,
                    userAliasList,
                    currentUserName
                );

                if (dataModelResult.isSuccess()) {
                    successMessages.add("数据模型权限分配成功");
                } else {
                    errorMessages.add("数据模型权限分配失败: " + assistant.getDataModelId());
                }
            }

            // 2. 分配数据源权限
            if (assistant.getConnectionId() != null) {
                ServiceResp dataSourceResult = assignPermissionsToResource(
                    DbConnectionConstant.RESOURCE_TYPE_DB_CONNECTION,
                    assistant.getConnectionId().toString(),
                    permissionType,
                    userType,
                    userIdList,
                    userNameList,
                    userAliasList,
                    currentUserName
                );

                if (dataSourceResult.isSuccess()) {
                    successMessages.add("数据源权限分配成功");
                } else {
                    errorMessages.add("数据源权限分配失败: " + assistant.getConnectionId().toString());
                }
            } else if (StringUtils.isNotBlank(assistant.getDataModelId())) {
                // 如果助理没有直接关联数据源，但有数据模型，则从数据模型获取数据源
                AiDataModelD dataModel = aiDataModelService.getById(assistant.getDataModelId());
                if (dataModel != null && dataModel.getConnectionId() != null) {
                    ServiceResp dataSourceResult = assignPermissionsToResource(
                        DbConnectionConstant.RESOURCE_TYPE_DB_CONNECTION,
                        dataModel.getConnectionId().toString(),
                        permissionType,
                        userType,
                        userIdList,
                        userNameList,
                        userAliasList,
                        currentUserName
                    );

                    if (dataSourceResult.isSuccess()) {
                        successMessages.add("数据源权限分配成功(通过数据模型)");
                    } else {
                        errorMessages.add("数据源权限分配失败(通过数据模型): " + dataModel.getConnectionId().toString());
                    }
                }
            }

            // 构建返回消息
            StringBuilder message = new StringBuilder();
            if (!successMessages.isEmpty()) {
                message.append("成功: ").append(String.join(", ", successMessages));
            }
            if (!errorMessages.isEmpty()) {
                if (message.length() > 0) {
                    message.append("; ");
                }
                message.append("失败: ").append(String.join(", ", errorMessages));
            }

            return errorMessages.isEmpty() ?
                ServiceResp.getInstance().success(message.toString()) :
                ServiceResp.getInstance().error(message.toString());

        } catch (Exception e) {
            log.error("自动分配关联权限时发生异常", e);
            return ServiceResp.getInstance().error("自动分配关联权限失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ServiceResp cleanupUnusedRelatedPermissions(Integer resourceType, String resourceId,
                                                      Integer userType, String userId, String userName,
                                                      String currentUserName) {
        try {
            // 只处理助理权限的删除
            if (!resourceType.equals(CommonConstant.RESOURCE_TYPE_ASSISTANT)) {
                return ServiceResp.getInstance().success("非助理权限，无需清理关联权限");
            }

            // 获取助理信息
            AiAssistantD assistant = aiAssistantService.getById(Long.valueOf(resourceId));
            if (assistant == null) {
                log.warn("助理不存在，无法清理关联权限: assistantId={}", resourceId);
                return ServiceResp.getInstance().success("助理不存在，跳过关联权限清理");
            }

            List<String> successMessages = new ArrayList<>();
            List<String> errorMessages = new ArrayList<>();

            // 1. 检查并清理数据模型权限
            if (StringUtils.isNotBlank(assistant.getDataModelId())) {
                boolean shouldCleanupDataModel = !hasOtherAssistantUsingDataModel(assistant.getDataModelId(), resourceId, userType, userId);
                if (shouldCleanupDataModel) {
                    ServiceResp cleanupResult = removePermissionIfExists(
                        CommonConstant.RESOURCE_TYPE_DATA_MODEL,
                        assistant.getDataModelId(),
                        userType,
                        userId,
                        userName
                    );

                    if (cleanupResult.isSuccess()) {
                        successMessages.add("数据模型权限清理成功");
                    } else {
                        errorMessages.add("数据模型权限清理失败: " + assistant.getDataModelId());
                    }
                }
            }

            // 2. 检查并清理数据源权限
            String connectionId = null;
            if (assistant.getConnectionId() != null) {
                connectionId = assistant.getConnectionId().toString();
            } else if (StringUtils.isNotBlank(assistant.getDataModelId())) {
                // 从数据模型获取数据源ID
                AiDataModelD dataModel = aiDataModelService.getById(assistant.getDataModelId());
                if (dataModel != null && dataModel.getConnectionId() != null) {
                    connectionId = dataModel.getConnectionId().toString();
                }
            }

            if (StringUtils.isNotBlank(connectionId)) {
                boolean shouldCleanupDataSource = !hasOtherAssistantUsingDataSource(connectionId, resourceId, userType, userId);
                if (shouldCleanupDataSource) {
                    ServiceResp cleanupResult = removePermissionIfExists(
                        DbConnectionConstant.RESOURCE_TYPE_DB_CONNECTION,
                        connectionId,
                        userType,
                        userId,
                        userName
                    );

                    if (cleanupResult.isSuccess()) {
                        successMessages.add("数据源权限清理成功");
                    } else {
                        errorMessages.add("数据源权限清理失败: " + connectionId);
                    }
                }
            }

            // 构建返回消息
            StringBuilder message = new StringBuilder();
            if (!successMessages.isEmpty()) {
                message.append("成功: ").append(String.join(", ", successMessages));
            }
            if (!errorMessages.isEmpty()) {
                if (message.length() > 0) {
                    message.append("; ");
                }
                message.append("失败: ").append(String.join(", ", errorMessages));
            }

            if (message.length() == 0) {
                message.append("无需清理关联权限");
            }

            return errorMessages.isEmpty() ?
                ServiceResp.getInstance().success(message.toString()) :
                ServiceResp.getInstance().error(message.toString());

        } catch (Exception e) {
            log.error("清理关联权限时发生异常", e);
            return ServiceResp.getInstance().error("清理关联权限失败: " + e.getMessage());
        }
    }

    /**
     * 为指定资源分配权限
     */
    private ServiceResp assignPermissionsToResource(Integer resourceType, String resourceId,
                                                   Integer permissionType, Integer userType,
                                                   List<String> userIdList, List<String> userNameList,
                                                   List<String> userAliasList, String currentUserName) {
        try {
            List<AiPermissionConfigD> permissionList = new ArrayList<>();
            LocalDateTime now = LocalDateTime.now();

            for (int i = 0; i < userIdList.size(); i++) {
                String userId = userIdList.get(i);
                String userName = userNameList.get(i);
                String userAlias = null;
                if (userAliasList != null && i < userAliasList.size()) {
                    userAlias = userAliasList.get(i);
                }

                // 检查权限是否已存在
                LambdaQueryWrapper<AiPermissionConfigD> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(AiPermissionConfigD::getResourceType, resourceType)
                        .eq(AiPermissionConfigD::getResourceId, resourceId)
                        .eq(AiPermissionConfigD::getUserType, userType)
                        .eq(AiPermissionConfigD::getUserId, userId);

                if (this.count(queryWrapper) > 0) {
                    // 权限已存在，跳过
                    continue;
                }

                // 创建新的权限配置
                AiPermissionConfigD permission = new AiPermissionConfigD();
                permission.setResourceType(resourceType);
                permission.setResourceId(resourceId);
                permission.setPermissionType(permissionType);
                permission.setUserType(userType);
                permission.setUserId(userId);
                permission.setUserName(userName);
                permission.setUserAlias(userAlias);
                permission.setCreateUserName(currentUserName);
                permission.setCreateTime(now);
                permission.setUpdateUserName(currentUserName);
                permission.setUpdateTime(now);

                permissionList.add(permission);
            }

            if (!permissionList.isEmpty()) {
                boolean result = this.saveBatch(permissionList);
                return result ?
                    ServiceResp.getInstance().success("权限分配成功") :
                    ServiceResp.getInstance().error("权限分配失败");
            } else {
                return ServiceResp.getInstance().success("权限已存在，无需重复分配");
            }

        } catch (Exception e) {
            log.error("分配资源权限时发生异常", e);
            return ServiceResp.getInstance().error("分配资源权限失败: " + e.getMessage());
        }
    }

    /**
     * 检查用户是否还有其他助理使用指定的数据模型
     */
    private boolean hasOtherAssistantUsingDataModel(String dataModelId, String excludeAssistantId,
                                                   Integer userType, String userId) {
        try {
            // 查询用户有权限的所有助理
            LambdaQueryWrapper<AiPermissionConfigD> permissionQuery = new LambdaQueryWrapper<>();
            permissionQuery.eq(AiPermissionConfigD::getResourceType, CommonConstant.RESOURCE_TYPE_ASSISTANT)
                    .eq(AiPermissionConfigD::getUserType, userType)
                    .eq(AiPermissionConfigD::getUserId, userId)
                    .ne(AiPermissionConfigD::getResourceId, excludeAssistantId);

            List<AiPermissionConfigD> assistantPermissions = this.list(permissionQuery);

            for (AiPermissionConfigD permission : assistantPermissions) {
                AiAssistantD assistant = aiAssistantService.getById(Long.valueOf(permission.getResourceId()));
                if (assistant != null && dataModelId.equals(assistant.getDataModelId())) {
                    return true;
                }
            }

            return false;
        } catch (Exception e) {
            log.error("检查其他助理使用数据模型时发生异常", e);
            return true; // 出错时保守处理，不删除权限
        }
    }

    /**
     * 检查用户是否还有其他助理使用指定的数据源
     */
    private boolean hasOtherAssistantUsingDataSource(String connectionId, String excludeAssistantId,
                                                    Integer userType, String userId) {
        try {
            // 查询用户有权限的所有助理
            LambdaQueryWrapper<AiPermissionConfigD> permissionQuery = new LambdaQueryWrapper<>();
            permissionQuery.eq(AiPermissionConfigD::getResourceType, CommonConstant.RESOURCE_TYPE_ASSISTANT)
                    .eq(AiPermissionConfigD::getUserType, userType)
                    .eq(AiPermissionConfigD::getUserId, userId)
                    .ne(AiPermissionConfigD::getResourceId, excludeAssistantId);

            List<AiPermissionConfigD> assistantPermissions = this.list(permissionQuery);

            for (AiPermissionConfigD permission : assistantPermissions) {
                AiAssistantD assistant = aiAssistantService.getById(Long.valueOf(permission.getResourceId()));
                if (assistant != null) {
                    // 检查助理直接关联的数据源
                    if (assistant.getConnectionId() != null &&
                        connectionId.equals(assistant.getConnectionId().toString())) {
                        return true;
                    }

                    // 检查助理通过数据模型关联的数据源
                    if (StringUtils.isNotBlank(assistant.getDataModelId())) {
                        AiDataModelD dataModel = aiDataModelService.getById(assistant.getDataModelId());
                        if (dataModel != null && dataModel.getConnectionId() != null &&
                            connectionId.equals(dataModel.getConnectionId().toString())) {
                            return true;
                        }
                    }
                }
            }

            return false;
        } catch (Exception e) {
            log.error("检查其他助理使用数据源时发生异常", e);
            return true; // 出错时保守处理，不删除权限
        }
    }

    /**
     * 删除指定的权限配置（如果存在）
     */
    private ServiceResp removePermissionIfExists(Integer resourceType, String resourceId,
                                               Integer userType, String userId, String userName) {
        try {
            LambdaQueryWrapper<AiPermissionConfigD> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AiPermissionConfigD::getResourceType, resourceType)
                    .eq(AiPermissionConfigD::getResourceId, resourceId)
                    .eq(AiPermissionConfigD::getUserType, userType)
                    .eq(AiPermissionConfigD::getUserId, userId);

            List<AiPermissionConfigD> permissions = this.list(queryWrapper);
            if (!permissions.isEmpty()) {
                boolean result = this.remove(queryWrapper);
                return result ?
                    ServiceResp.getInstance().success("权限删除成功") :
                    ServiceResp.getInstance().error("权限删除失败");
            } else {
                return ServiceResp.getInstance().success("权限不存在，无需删除");
            }

        } catch (Exception e) {
            log.error("删除权限时发生异常", e);
            return ServiceResp.getInstance().error("删除权限失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ServiceResp handleAssistantUpdatePermissions(String assistantId, AiAssistantD oldAssistant,
                                                       AiAssistantD newAssistant, String currentUserName) {
        try {
            log.info("开始处理助理修改时的权限重新分配: assistantId={}", assistantId);

            // 检查数据模型是否发生变化
            boolean dataModelChanged = hasDataModelChanged(oldAssistant, newAssistant);
            // 检查数据源是否发生变化
            boolean dataSourceChanged = hasDataSourceChanged(oldAssistant, newAssistant);

            if (!dataModelChanged && !dataSourceChanged) {
                log.info("助理的数据模型和数据源均未发生变化，无需重新分配权限: assistantId={}", assistantId);
                return ServiceResp.getInstance().success("数据模型和数据源未变化，无需重新分配权限");
            }

            // 获取拥有该助理权限的所有用户
            List<AiPermissionConfigD> assistantPermissions = getAssistantPermissions(assistantId);
            if (assistantPermissions.isEmpty()) {
                log.info("助理没有权限配置，无需处理关联权限: assistantId={}", assistantId);
                return ServiceResp.getInstance().success("助理无权限配置，无需处理");
            }

            List<String> successMessages = new ArrayList<>();
            List<String> errorMessages = new ArrayList<>();

            // 为每个拥有助理权限的用户重新分配关联权限
            for (AiPermissionConfigD permission : assistantPermissions) {
                ServiceResp result = reassignRelatedPermissionsForUser(
                    assistantId, oldAssistant, newAssistant, permission,
                    dataModelChanged, dataSourceChanged, currentUserName);

                if (result.isSuccess()) {
                    successMessages.add("用户 " + permission.getUserName() + " 权限重新分配成功");
                } else {
                    errorMessages.add("用户 " + permission.getUserName() + " 权限重新分配失败: " + result.getBody());
                }
            }

            // 构建返回结果
            StringBuilder resultMessage = new StringBuilder();
            if (!successMessages.isEmpty()) {
                resultMessage.append("成功: ").append(String.join(", ", successMessages));
            }
            if (!errorMessages.isEmpty()) {
                if (resultMessage.length() > 0) {
                    resultMessage.append("; ");
                }
                resultMessage.append("失败: ").append(String.join(", ", errorMessages));
            }

            boolean hasErrors = !errorMessages.isEmpty();
            log.info("助理权限重新分配完成: assistantId={}, 结果={}", assistantId, resultMessage.toString());

            return hasErrors ?
                ServiceResp.getInstance().error(resultMessage.toString()) :
                ServiceResp.getInstance().success(resultMessage.toString());

        } catch (Exception e) {
            log.error("处理助理修改权限重新分配时发生异常: assistantId={}", assistantId, e);
            return ServiceResp.getInstance().error("权限重新分配失败: " + e.getMessage());
        }
    }

    /**
     * 检查数据模型是否发生变化
     */
    private boolean hasDataModelChanged(AiAssistantD oldAssistant, AiAssistantD newAssistant) {
        String oldDataModelId = oldAssistant.getDataModelId();
        String newDataModelId = newAssistant.getDataModelId();

        // 都为空，未变化
        if (StringUtils.isBlank(oldDataModelId) && StringUtils.isBlank(newDataModelId)) {
            return false;
        }

        // 一个为空一个不为空，发生变化
        if (StringUtils.isBlank(oldDataModelId) || StringUtils.isBlank(newDataModelId)) {
            return true;
        }

        // 都不为空，比较是否相等
        return !oldDataModelId.equals(newDataModelId);
    }

    /**
     * 检查数据源是否发生变化
     */
    private boolean hasDataSourceChanged(AiAssistantD oldAssistant, AiAssistantD newAssistant) {
        BigInteger oldConnectionId = oldAssistant.getConnectionId();
        BigInteger newConnectionId = newAssistant.getConnectionId();

        // 都为空，未变化
        if (oldConnectionId == null && newConnectionId == null) {
            return false;
        }

        // 一个为空一个不为空，发生变化
        if (oldConnectionId == null || newConnectionId == null) {
            return true;
        }

        // 都不为空，比较是否相等
        return !oldConnectionId.equals(newConnectionId);
    }

    /**
     * 获取拥有助理权限的所有用户
     */
    private List<AiPermissionConfigD> getAssistantPermissions(String assistantId) {
        LambdaQueryWrapper<AiPermissionConfigD> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiPermissionConfigD::getResourceType, CommonConstant.RESOURCE_TYPE_ASSISTANT)
                .eq(AiPermissionConfigD::getResourceId, assistantId);
        return this.list(queryWrapper);
    }

    /**
     * 为单个用户重新分配关联权限
     */
    private ServiceResp reassignRelatedPermissionsForUser(String assistantId, AiAssistantD oldAssistant,
                                                         AiAssistantD newAssistant, AiPermissionConfigD permission,
                                                         boolean dataModelChanged, boolean dataSourceChanged,
                                                         String currentUserName) {
        try {
            List<String> successMessages = new ArrayList<>();
            List<String> errorMessages = new ArrayList<>();

            // 处理数据模型权限变化
            if (dataModelChanged) {
                ServiceResp dataModelResult = handleDataModelPermissionChange(
                    oldAssistant, newAssistant, permission, currentUserName);
                if (dataModelResult.isSuccess()) {
                    successMessages.add("数据模型权限处理成功");
                } else {
                    errorMessages.add("数据模型权限处理失败: " + dataModelResult.getBody());
                }
            }

            // 处理数据源权限变化
            if (dataSourceChanged) {
                ServiceResp dataSourceResult = handleDataSourcePermissionChange(
                    oldAssistant, newAssistant, permission, currentUserName);
                if (dataSourceResult.isSuccess()) {
                    successMessages.add("数据源权限处理成功");
                } else {
                    errorMessages.add("数据源权限处理失败: " + dataSourceResult.getBody());
                }
            }

            // 构建返回结果
            StringBuilder resultMessage = new StringBuilder();
            if (!successMessages.isEmpty()) {
                resultMessage.append(String.join(", ", successMessages));
            }
            if (!errorMessages.isEmpty()) {
                if (resultMessage.length() > 0) {
                    resultMessage.append("; ");
                }
                resultMessage.append(String.join(", ", errorMessages));
            }

            boolean hasErrors = !errorMessages.isEmpty();
            return hasErrors ?
                ServiceResp.getInstance().error(resultMessage.toString()) :
                ServiceResp.getInstance().success(resultMessage.toString());

        } catch (Exception e) {
            log.error("为用户重新分配关联权限时发生异常: userId={}", permission.getUserId(), e);
            return ServiceResp.getInstance().error("权限重新分配失败: " + e.getMessage());
        }
    }

    /**
     * 处理数据模型权限变化
     */
    private ServiceResp handleDataModelPermissionChange(AiAssistantD oldAssistant, AiAssistantD newAssistant,
                                                       AiPermissionConfigD permission, String currentUserName) {
        try {
            List<String> successMessages = new ArrayList<>();
            List<String> errorMessages = new ArrayList<>();

            // 移除旧数据模型权限
            if (StringUtils.isNotBlank(oldAssistant.getDataModelId())) {
                ServiceResp removeResult = removeUserPermissionFromResource(
                    CommonConstant.RESOURCE_TYPE_DATA_MODEL,
                    oldAssistant.getDataModelId(),
                    permission,
                    currentUserName
                );
                if (removeResult.isSuccess()) {
                    successMessages.add("移除旧数据模型权限成功");
                } else {
                    errorMessages.add("移除旧数据模型权限失败: " + removeResult.getBody());
                }
            }

            // 添加新数据模型权限
            if (StringUtils.isNotBlank(newAssistant.getDataModelId())) {
                ServiceResp addResult = assignPermissionsToResource(
                    CommonConstant.RESOURCE_TYPE_DATA_MODEL,
                    newAssistant.getDataModelId(),
                    permission.getPermissionType(),
                    permission.getUserType(),
                    Arrays.asList(permission.getUserId()),
                    Arrays.asList(permission.getUserName()),
                    Arrays.asList(permission.getUserAlias()),
                    currentUserName
                );
                if (addResult.isSuccess()) {
                    successMessages.add("添加新数据模型权限成功");
                } else {
                    errorMessages.add("添加新数据模型权限失败: " + addResult.getBody());
                }
            }

            // 构建返回结果
            StringBuilder resultMessage = new StringBuilder();
            if (!successMessages.isEmpty()) {
                resultMessage.append(String.join(", ", successMessages));
            }
            if (!errorMessages.isEmpty()) {
                if (resultMessage.length() > 0) {
                    resultMessage.append("; ");
                }
                resultMessage.append(String.join(", ", errorMessages));
            }

            boolean hasErrors = !errorMessages.isEmpty();
            return hasErrors ?
                ServiceResp.getInstance().error(resultMessage.toString()) :
                ServiceResp.getInstance().success(resultMessage.toString());

        } catch (Exception e) {
            log.error("处理数据模型权限变化时发生异常", e);
            return ServiceResp.getInstance().error("数据模型权限处理失败: " + e.getMessage());
        }
    }

    /**
     * 处理数据源权限变化
     */
    private ServiceResp handleDataSourcePermissionChange(AiAssistantD oldAssistant, AiAssistantD newAssistant,
                                                        AiPermissionConfigD permission, String currentUserName) {
        try {
            List<String> successMessages = new ArrayList<>();
            List<String> errorMessages = new ArrayList<>();

            // 获取旧的数据源ID（直接关联或通过数据模型关联）
            String oldDataSourceId = getEffectiveDataSourceId(oldAssistant);
            // 获取新的数据源ID（直接关联或通过数据模型关联）
            String newDataSourceId = getEffectiveDataSourceId(newAssistant);

            // 移除旧数据源权限
            if (StringUtils.isNotBlank(oldDataSourceId)) {
                ServiceResp removeResult = removeUserPermissionFromResource(
                    DbConnectionConstant.RESOURCE_TYPE_DB_CONNECTION,
                    oldDataSourceId,
                    permission,
                    currentUserName
                );
                if (removeResult.isSuccess()) {
                    successMessages.add("移除旧数据源权限成功");
                } else {
                    errorMessages.add("移除旧数据源权限失败: " + removeResult.getBody());
                }
            }

            // 添加新数据源权限
            if (StringUtils.isNotBlank(newDataSourceId)) {
                ServiceResp addResult = assignPermissionsToResource(
                    DbConnectionConstant.RESOURCE_TYPE_DB_CONNECTION,
                    newDataSourceId,
                    permission.getPermissionType(),
                    permission.getUserType(),
                    Arrays.asList(permission.getUserId()),
                    Arrays.asList(permission.getUserName()),
                    Arrays.asList(permission.getUserAlias()),
                    currentUserName
                );
                if (addResult.isSuccess()) {
                    successMessages.add("添加新数据源权限成功");
                } else {
                    errorMessages.add("添加新数据源权限失败: " + addResult.getBody());
                }
            }

            // 构建返回结果
            StringBuilder resultMessage = new StringBuilder();
            if (!successMessages.isEmpty()) {
                resultMessage.append(String.join(", ", successMessages));
            }
            if (!errorMessages.isEmpty()) {
                if (resultMessage.length() > 0) {
                    resultMessage.append("; ");
                }
                resultMessage.append(String.join(", ", errorMessages));
            }

            boolean hasErrors = !errorMessages.isEmpty();
            return hasErrors ?
                ServiceResp.getInstance().error(resultMessage.toString()) :
                ServiceResp.getInstance().success(resultMessage.toString());

        } catch (Exception e) {
            log.error("处理数据源权限变化时发生异常", e);
            return ServiceResp.getInstance().error("数据源权限处理失败: " + e.getMessage());
        }
    }

    /**
     * 获取助理的有效数据源ID（直接关联或通过数据模型关联）
     */
    private String getEffectiveDataSourceId(AiAssistantD assistant) {
        // 优先使用直接关联的数据源
        if (assistant.getConnectionId() != null) {
            return assistant.getConnectionId().toString();
        }

        // 如果没有直接关联数据源，但有数据模型，则从数据模型获取数据源
        if (StringUtils.isNotBlank(assistant.getDataModelId())) {
            AiDataModelD dataModel = aiDataModelService.getById(assistant.getDataModelId());
            if (dataModel != null && dataModel.getConnectionId() != null) {
                return dataModel.getConnectionId().toString();
            }
        }

        return null;
    }

    /**
     * 移除用户对特定资源的权限
     */
    private ServiceResp removeUserPermissionFromResource(Integer resourceType, String resourceId,
                                                        AiPermissionConfigD permission, String currentUserName) {
        try {
            // 查找要删除的权限配置
            LambdaQueryWrapper<AiPermissionConfigD> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AiPermissionConfigD::getResourceType, resourceType)
                    .eq(AiPermissionConfigD::getResourceId, resourceId)
                    .eq(AiPermissionConfigD::getUserType, permission.getUserType())
                    .eq(AiPermissionConfigD::getUserId, permission.getUserId())
                    .eq(AiPermissionConfigD::getUserName, permission.getUserName())
                    .eq(AiPermissionConfigD::getPermissionType, permission.getPermissionType());

            List<AiPermissionConfigD> existingPermissions = this.list(queryWrapper);
            if (existingPermissions.isEmpty()) {
                return ServiceResp.getInstance().success("权限不存在，无需删除");
            }

            // 删除权限配置
            boolean result = this.remove(queryWrapper);
            if (result) {
                log.info("成功移除用户权限: resourceType={}, resourceId={}, userId={}",
                        resourceType, resourceId, permission.getUserId());
                return ServiceResp.getInstance().success("权限移除成功");
            } else {
                return ServiceResp.getInstance().error("权限移除失败");
            }

        } catch (Exception e) {
            log.error("移除用户权限时发生异常: resourceType={}, resourceId={}, userId={}",
                    resourceType, resourceId, permission.getUserId(), e);
            return ServiceResp.getInstance().error("权限移除失败: " + e.getMessage());
        }
    }
}