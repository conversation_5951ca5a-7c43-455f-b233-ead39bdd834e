package com.ffcs.oss.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 权限配置表
 */
@Data
@TableName("ai_permission_config_d")
public class AiPermissionConfigD {
    
    /**
     * 主键
     */
    @TableId(value = "permission_id", type = IdType.AUTO)
    private Long permissionId;
    
    /**
     * 资源类型：1-数据模型，2-助理，3-数据源
     */
    @TableField("resource_type")
    private Integer resourceType;
    
    /**
     * 资源ID
     */
    @TableField("resource_id")
    private String resourceId;
    
    /**
     * 权限类型：1-管理员，2-普通用户
     */
    @TableField("permission_type")
    private Integer permissionType;
    
    /**
     * 用户/用户组类型：1-用户，2-用户组
     */
    @TableField("user_type")
    private Integer userType;
    
    /**
     * 用户/用户组ID
     */
    @TableField("user_id")
    private String userId;
    
    /**
     * 用户/用户组名称
     */
    @TableField("user_name")
    private String userName;

    /**
     * 用户/用户组别名
     */
    @TableField("user_alias")
    private String userAlias;
    
    /**
     * 创建人
     */
    @TableField("create_user_name")
    private String createUserName;
    
    /**
     * 创建时间
     */
    @TableField("create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;
    
    /**
     * 更新人
     */
    @TableField("update_user_name")
    private String updateUserName;
    
    /**
     * 更新时间
     */
    @TableField("update_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;
} 