package com.ffcs.oss.web.rest.vm.permission;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 权限配置视图模型
 */
@ApiModel("权限配置视图模型")
public class PermissionConfigVm implements Serializable {
    
    @ApiModelProperty("权限ID")
    private Long permissionId;
    
    @ApiModelProperty("资源类型：1-数据模型，2-助理，3-数据源")
    private Integer resourceType;
    
    @ApiModelProperty("资源ID")
    private String resourceId;
    
    @ApiModelProperty("资源名称")
    private String resourceName;
    
    @ApiModelProperty("权限类型：1-管理员，2-普通用户")
    private Integer permissionType;
    
    @ApiModelProperty("用户/用户组类型：1-用户，2-用户组")
    private Integer userType;
    
    @ApiModelProperty("用户/用户组ID")
    private String userId;
    
    @ApiModelProperty("用户/用户组名称")
    private String userName;

    @ApiModelProperty("用户/用户组别名")
    private String userAlias;
    
    @ApiModelProperty("创建人")
    private String createUserName;
    
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;
    
    @ApiModelProperty("更新人")
    private String updateUserName;
    
    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;
    
    @ApiModelProperty("是否为创建者")
    private Boolean isCreator;
    
    public Long getPermissionId() {
        return permissionId;
    }
    
    public void setPermissionId(Long permissionId) {
        this.permissionId = permissionId;
    }
    
    public Integer getResourceType() {
        return resourceType;
    }
    
    public void setResourceType(Integer resourceType) {
        this.resourceType = resourceType;
    }
    
    public String getResourceId() {
        return resourceId;
    }
    
    public void setResourceId(String resourceId) {
        this.resourceId = resourceId;
    }
    
    public String getResourceName() {
        return resourceName;
    }
    
    public void setResourceName(String resourceName) {
        this.resourceName = resourceName;
    }
    
    public Integer getPermissionType() {
        return permissionType;
    }
    
    public void setPermissionType(Integer permissionType) {
        this.permissionType = permissionType;
    }
    
    public Integer getUserType() {
        return userType;
    }
    
    public void setUserType(Integer userType) {
        this.userType = userType;
    }
    
    public String getUserId() {
        return userId;
    }
    
    public void setUserId(String userId) {
        this.userId = userId;
    }
    
    public String getUserName() {
        return userName;
    }
    
    public void setUserName(String userName) {
        this.userName = userName;
    }
    
    public String getCreateUserName() {
        return createUserName;
    }
    
    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }
    
    public LocalDateTime getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    
    public String getUpdateUserName() {
        return updateUserName;
    }
    
    public void setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
    }
    
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }
    
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
    
    public Boolean getIsCreator() {
        return isCreator;
    }
    
    public void setIsCreator(Boolean creator) {
        isCreator = creator;
    }

    public String getUserAlias() {
        return userAlias;
    }

    public void setUserAlias(String userAlias) {
        this.userAlias = userAlias;
    }
}