package com.ffcs.oss.web.rest.evt.permission;

import com.ffcs.oss.param.in.QueryPageEvt;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * 权限配置请求参数
 */
@ApiModel("权限配置请求参数")
public class PermissionConfigEvt extends QueryPageEvt implements Serializable {
    
    @ApiModelProperty("权限ID")
    private Long permissionId;
    
    @ApiModelProperty("资源类型：1-数据模型，2-助理，3-数据源")
    private Integer resourceType;
    
    @ApiModelProperty("资源ID")
    private String resourceId;
    
    @ApiModelProperty("权限类型：1-管理员，2-普通用户")
    private Integer permissionType;
    
    @ApiModelProperty("用户/用户组类型：1-用户，2-用户组")
    private Integer userType;
    
    @ApiModelProperty("用户/用户组ID")
    private String userId;
    
    @ApiModelProperty("用户/用户组名称")
    private String userName;
    
    @ApiModelProperty("用户/用户组ID列表")
    private List<String> userIdList;
    
    @ApiModelProperty("用户/用户组名称列表")
    private List<String> userNameList;

    @ApiModelProperty("用户/用户组别名列表")
    private List<String> userAliasList;
    
    @ApiModelProperty("搜索关键字")
    private String keyword;
    
    @ApiModelProperty("创建人")
    private String createUserName;
    
    @ApiModelProperty("更新人")
    private String updateUserName;

    public Long getPermissionId() {
        return permissionId;
    }

    public void setPermissionId(Long permissionId) {
        this.permissionId = permissionId;
    }

    public Integer getResourceType() {
        return resourceType;
    }

    public void setResourceType(Integer resourceType) {
        this.resourceType = resourceType;
    }

    public String getResourceId() {
        return resourceId;
    }

    public void setResourceId(String resourceId) {
        this.resourceId = resourceId;
    }

    public Integer getPermissionType() {
        return permissionType;
    }

    public void setPermissionType(Integer permissionType) {
        this.permissionType = permissionType;
    }

    public Integer getUserType() {
        return userType;
    }

    public void setUserType(Integer userType) {
        this.userType = userType;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public List<String> getUserIdList() {
        return userIdList;
    }

    public void setUserIdList(List<String> userIdList) {
        this.userIdList = userIdList;
    }

    public List<String> getUserNameList() {
        return userNameList;
    }

    public void setUserNameList(List<String> userNameList) {
        this.userNameList = userNameList;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

    public String getUpdateUserName() {
        return updateUserName;
    }

    public void setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
    }

    public List<String> getUserAliasList() {
        return userAliasList;
    }

    public void setUserAliasList(List<String> userAliasList) {
        this.userAliasList = userAliasList;
    }
}